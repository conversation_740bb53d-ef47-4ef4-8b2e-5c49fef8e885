import { redirect } from "next/navigation"
import { createServerClient } from "@/lib/supabase"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"

export default async function DashboardPage() {
  const supabase = await createServerClient()

  // Handle case where Supabase is not configured (placeholder)
  if (!supabase) {
    return <DashboardLayout initialBusiness={null} />
  }

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      redirect("/auth/login")
    }

    // Check if user has a business profile
    const { data: business } = await supabase.from("businesses").select("*").eq("owner_id", user.id).single()

    return <DashboardLayout initialBusiness={business} />
  } catch (error) {
    // Handle Supabase errors gracefully
    console.error("Supabase error:", error)
    return <DashboardLayout initialBusiness={null} />
  }
}
