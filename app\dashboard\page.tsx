"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/hooks/use-user"
import { supabase } from "@/lib/supabase"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Loader2 } from "lucide-react"
import type { Business } from "@/lib/types"

export default function DashboardPage() {
  const router = useRouter()
  const { user, loading } = useUser()
  const [business, setBusiness] = useState<Business | null>(null)
  const [businessLoading, setBusinessLoading] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      console.log("No user found, redirecting to login")
      router.push("/auth/login")
      return
    }

    if (user && supabase) {
      fetchBusiness()
    }
  }, [user, loading, router])

  const fetchBusiness = async () => {
    if (!user || !supabase) return

    try {
      setBusinessLoading(true)
      const { data, error } = await supabase
        .from("businesses")
        .select("*")
        .eq("owner_id", user.id)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error("Error fetching business:", error)
      } else {
        setBusiness(data || null)
      }
    } catch (error) {
      console.error("Business fetch error:", error)
    } finally {
      setBusinessLoading(false)
    }
  }

  // Show loading while checking authentication
  if (loading || businessLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  // Don't render anything if no user (will redirect)
  if (!user) {
    return null
  }

  return <DashboardLayout initialBusiness={business} />
}
