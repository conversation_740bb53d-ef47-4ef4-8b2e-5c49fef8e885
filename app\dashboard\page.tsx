import { redirect } from "next/navigation"
import { createServerClient } from "@/lib/supabase"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"

export default async function DashboardPage() {
  const supabase = await createServerClient()

  // Handle case where Supa<PERSON> is not configured (placeholder)
  if (!supabase) {
    return <DashboardLayout initialBusiness={null} />
  }

  try {
    // Try to get both user and session for better reliability
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    console.log("Dashboard auth check:", {
      hasUser: !!user,
      hasSession: !!session,
      userError: userError?.message,
      sessionError: sessionError?.message
    })

    // Only redirect if we're sure there's no authentication
    if (!user && !session) {
      console.log("No user or session found, redirecting to login")
      redirect("/auth/login")
    }

    // If we have a session but no user, try to use session user
    const authenticatedUser = user || session?.user

    if (!authenticatedUser) {
      console.log("No authenticated user found, redirecting to login")
      redirect("/auth/login")
    }

    // Check if user has a business profile
    const { data: business, error: businessError } = await supabase
      .from("businesses")
      .select("*")
      .eq("owner_id", authenticatedUser.id)
      .single()

    if (businessError && businessError.code !== 'PGRST116') {
      console.error("Error fetching business:", businessError)
    }

    return <DashboardLayout initialBusiness={business || null} />
  } catch (error) {
    // Handle Supabase errors gracefully
    console.error("Dashboard error:", error)
    // Don't redirect on errors, just show empty dashboard
    return <DashboardLayout initialBusiness={null} />
  }
}
