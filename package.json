{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:integration": "node scripts/test-integration.js", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:seed": "SEED_TEST_DB=true vitest run tests/database", "test:cleanup": "node -e \"require('./tests/database/seed-manager.js').TestSeedManager().cleanupDatabase()\"", "test:reset": "node -e \"require('./tests/database/seed-manager.js').TestSeedManager().resetDatabase()\"", "test:setup": "node scripts/setup-test-env.js", "test:all": "node scripts/run-all-tests.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.0", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "dotenv": "^16.3.0", "jsdom": "^23.0.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^1.0.0"}}