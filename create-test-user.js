#!/usr/bin/env node

/**
 * Create a test user profile for bulk import testing
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function createTestUser() {
  console.log('👤 Creating Test User for Bulk Import')
  console.log('====================================')

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables')
    return null
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey)

  try {
    // Generate a test UUID
    const testUserId = '12345678-1234-1234-1234-123456789012'
    
    // Check if test user already exists
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', testUserId)
      .single()

    if (existingUser) {
      console.log('✅ Test user already exists')
      console.log(`📋 User ID: ${testUserId}`)
      console.log(`👤 Name: ${existingUser.full_name}`)
      return testUserId
    }

    // Create test user profile
    const { data, error } = await supabase
      .from('profiles')
      .insert({
        id: testUserId,
        full_name: 'Test Admin User',
        role: 'admin'
      })
      .select()
      .single()

    if (error) {
      console.error('❌ Failed to create test user:', error.message)
      return null
    }

    console.log('✅ Test user created successfully!')
    console.log(`📋 User ID: ${testUserId}`)
    console.log(`👤 Name: Test Admin User`)
    console.log(`🔑 Role: admin`)
    
    console.log('\n📝 Use this User ID in bulk import:')
    console.log(`   ${testUserId}`)

    return testUserId

  } catch (error) {
    console.error('❌ Error creating test user:', error.message)
    return null
  }
}

// Run the script
createTestUser()
  .then(userId => {
    if (userId) {
      console.log('\n🎉 Ready for bulk import!')
      console.log('   Copy the User ID above and use it in the bulk import form.')
    } else {
      console.log('\n⚠️  Failed to create test user. Please check the errors above.')
    }
  })
  .catch(error => {
    console.error('❌ Script failed:', error)
  })
