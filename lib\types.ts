// Database types matching the exact schema from the development guide

export interface Profile {
  id: string // UUID, references auth.users.id
  full_name?: string
  avatar_url?: string
  updated_at: string
}

export interface Business {
  id: string // UUID
  owner_id: string // UUID, references profiles.id
  name: string
  slug: string // URL-friendly unique identifier
  description?: string
  phone?: string
  website_url?: string
  avg_rating: number // Denormalized, default 0.0
  review_count: number // Denormalized, default 0
  created_at: string
  updated_at: string
}

export type BusinessRole = 'owner' | 'admin' | 'member'

export interface BusinessMember {
  business_id: string // UUID
  user_id: string // UUID
  role: BusinessRole
  joined_at: string
}

export interface Location {
  id: string // UUID
  business_id: string // UUID, unique
  street_address?: string
  city?: string
  state?: string
  zip_code?: string
  // coordinates?: Point // For PostGIS (future enhancement)
}

export interface Service {
  id: number // Serial
  name: string
  description?: string
}

export interface BusinessService {
  business_id: string // UUID
  service_id: number
  service?: Service
}

export interface PortfolioImage {
  id: string // UUID
  business_id: string // UUID
  image_url: string
  caption?: string
  display_order: number
  uploaded_at: string
}

export interface Review {
  id: string // UUID
  business_id: string // UUID
  author_id: string // UUID, references profiles.id
  rating: number // 1-5 stars
  content?: string
  created_at: string
  profile?: Profile
}

export interface MessageThread {
  id: string // UUID
  business_id: string // UUID
  user_id: string // UUID, the homeowner
  subject?: string
  status?: 'active' | 'closed' | 'archived'
  created_at: string
  updated_at: string
}

export interface Message {
  id: number // BigSerial
  thread_id: string // UUID
  author_id: string // UUID
  content: string
  attachments?: string[] // Array of file URLs
  read_at?: string // When message was read
  sent_at: string
  author?: Profile // Author profile information
}

export interface MessageThreadWithDetails extends MessageThread {
  business?: Business
  user?: Profile
  messages?: Message[]
}

// Extended types for UI components
export interface BusinessWithDetails extends Business {
  location?: Location
  services?: Service[]
  portfolio_images?: PortfolioImage[]
  reviews?: ReviewWithProfile[]
  owner?: Profile
  distance?: number // Distance in miles for location-based searches
}

export interface ReviewWithProfile extends Review {
  profile: Profile
}

export interface MessageThreadWithDetails extends MessageThread {
  messages?: Message[]
  business?: Business
  user?: Profile
}

// Legacy types for backward compatibility with existing frontend components
// These will be gradually migrated to the new schema
export interface GalleryImage {
  id: number
  business_id: string
  image_url: string
  caption?: string
  tag?: string
  created_at: string
}
