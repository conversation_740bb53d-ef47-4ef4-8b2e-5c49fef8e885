import { createServerClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }
    
    // Get current user and session
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (userError || sessionError) {
      console.error('Auth verification error:', userError || sessionError)
      return NextResponse.json(
        { authenticated: false, user: null, session: null },
        { status: 200 }
      )
    }
    
    if (!user || !session) {
      return NextResponse.json(
        { authenticated: false, user: null, session: null },
        { status: 200 }
      )
    }
    
    // Get user profile if authenticated
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Profile fetch error:', profileError)
    }
    
    return NextResponse.json({
      authenticated: true,
      user,
      session,
      profile: profile || null,
    })
    
  } catch (error) {
    console.error('Auth verification error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}