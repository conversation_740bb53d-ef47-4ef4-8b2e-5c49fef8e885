import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, addPortfolioImage } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { createServerClient } from '@/lib/supabase'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    
    // First get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner (this will also be enforced by RLS)
    if (business.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    const caption = formData.get('caption') as string
    const displayOrder = formData.get('display_order') as string
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.' 
      }, { status: 400 })
    }
    
    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'File too large. Maximum size is 5MB.' 
      }, { status: 400 })
    }
    
    const supabaseServer = await createServerClient()
    
    if (!supabaseServer) {
      return NextResponse.json({ error: 'Storage service unavailable' }, { status: 503 })
    }
    
    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${business.id}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    
    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseServer.storage
      .from('portfolio-images')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })
    
    if (uploadError) {
      console.error('Error uploading file:', uploadError)
      return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
    }
    
    // Get public URL
    const { data: { publicUrl } } = supabaseServer.storage
      .from('portfolio-images')
      .getPublicUrl(fileName)
    
    // Add to database
    const { image, error } = await addPortfolioImage(business.id, {
      image_url: publicUrl,
      caption: caption || undefined,
      display_order: displayOrder ? parseInt(displayOrder) : 0
    })
    
    if (error) {
      // If database insert fails, try to clean up the uploaded file
      await supabaseServer.storage
        .from('portfolio-images')
        .remove([fileName])
      
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ image }, { status: 201 })
  } catch (error) {
    console.error('Error uploading portfolio image:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}