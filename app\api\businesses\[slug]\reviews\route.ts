import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, createReview, getBusinessReviews, updateBusinessRating } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { withError<PERSON>and<PERSON>, validateRequest } from '@/lib/api-error-handler'
import { schemas } from '@/lib/validation-schemas'

export const GET = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) => {
  const { slug } = await params
  const { searchParams } = new URL(request.url)

  // Validate pagination parameters
  const paginationData = validateRequest(schemas.pagination, {
    page: parseInt(searchParams.get('page') || '1'),
    limit: parseInt(searchParams.get('limit') || '10')
  })

  // Calculate offset from page
  const offset = (paginationData.page - 1) * paginationData.limit

  // First get the business to get its ID
  const { business, error: businessError } = await getBusinessBySlug(slug)

  if (businessError || !business) {
    throw new Error('Business not found')
  }

  const { reviews, error } = await getBusinessReviews(business.id, paginationData.limit, offset)

  if (error) {
    throw error
  }

  return NextResponse.json({ reviews })
})

export const POST = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) => {
  await requireAuth()
  const { slug } = await params
  const body = await request.json()

  // Validate input
  const reviewData = validateRequest(schemas.review, {
    ...body,
    businessId: slug // We'll resolve this to actual ID later
  })

  // First get the business to get its ID
  const { business, error: businessError } = await getBusinessBySlug(slug)

  if (businessError || !business) {
    throw new Error('Business not found')
  }

  const { review, error } = await createReview({
    business_id: business.id,
    rating: reviewData.rating,
    content: reviewData.content
  })

  if (error) {
    throw error
  }

  // Update business rating automatically (this is also handled by database triggers)
  await updateBusinessRating(business.id)

  return NextResponse.json({
    review,
    message: 'Review created successfully'
  })
})