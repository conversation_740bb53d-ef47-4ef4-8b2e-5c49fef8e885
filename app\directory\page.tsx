import { Header } from "@/components/header"
import Link from "next/link"
import { ExternalLink, Users, Building2, FileText, Settings, Search, Phone, Shield, DollarSign, BookOpen, Home, Info, HelpCircle } from "lucide-react"

export default function DirectoryPage() {
  return (
    <div className="min-h-screen bg-black">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">Site Directory</h1>
            <p className="text-xl text-neutral-400">Complete navigation to every page on PressureWash Pro</p>
          </div>

          {/* Main Pages Section */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <Home className="h-6 w-6 text-blue-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Main Pages</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Link href="/" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-blue-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-blue-400">Home</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-blue-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Main landing page</p>
              </Link>

              <Link href="/search" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-blue-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-blue-400">Search Services</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-blue-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Find pressure washing services</p>
              </Link>

              <Link href="/how-it-works" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-blue-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-blue-400">How It Works</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-blue-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Learn about our process</p>
              </Link>

              <Link href="/for-businesses" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-blue-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-blue-400">For Businesses</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-blue-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Business information page</p>
              </Link>

              <Link href="/pricing" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-blue-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-blue-400">Pricing</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-blue-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Service pricing information</p>
              </Link>

              <Link href="/about" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-blue-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-blue-400">About Us</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-blue-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Company information</p>
              </Link>
            </div>
          </div>

          {/* Authentication Section */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <Users className="h-6 w-6 text-green-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Authentication</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Link href="/auth/login" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-green-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-green-400">Login</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-green-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">User login page</p>
              </Link>

              <Link href="/auth/signup" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-green-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-green-400">Sign Up</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-green-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Create new account</p>
              </Link>

              <Link href="/auth/forgot-password" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-green-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-green-400">Forgot Password</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-green-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Password recovery</p>
              </Link>
            </div>
          </div>

          {/* Business Portal Section */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <Building2 className="h-6 w-6 text-purple-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Business Portal</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Link href="/dashboard" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-purple-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-purple-400">Business Dashboard</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-purple-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Manage business listing</p>
              </Link>

              <Link href="/onboarding" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-purple-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-purple-400">Business Onboarding</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-purple-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Setup new business</p>
              </Link>

              <Link href="/business/elite-pressure-pros" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-purple-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-purple-400">Sample Business Profile</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-purple-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Example business page</p>
              </Link>
            </div>
          </div>

          {/* Content & Blog Section */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <BookOpen className="h-6 w-6 text-orange-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Content & Blog</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Link href="/blog" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-orange-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-orange-400">Blog</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-orange-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Blog listing page</p>
              </Link>

              <div className="bg-neutral-900 border border-neutral-700 rounded-xl p-4 opacity-75">
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300 font-medium">Blog Articles</span>
                  <Search className="h-4 w-4 text-neutral-500" />
                </div>
                <p className="text-sm text-neutral-500 mt-2">Dynamic: /blog/[id]</p>
              </div>
            </div>
          </div>

          {/* Support & Legal Section */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <HelpCircle className="h-6 w-6 text-yellow-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Support & Legal</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Link href="/contact" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-yellow-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-yellow-400">Contact Us</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-yellow-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Get in touch with us</p>
              </Link>

              <Link href="/terms" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-yellow-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-yellow-400">Terms of Service</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-yellow-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Legal terms and conditions</p>
              </Link>

              <Link href="/privacy" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-yellow-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-yellow-400">Privacy Policy</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-yellow-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Privacy and data protection</p>
              </Link>
            </div>
          </div>

          {/* Admin Panel Section */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <Settings className="h-6 w-6 text-red-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Admin Panel</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Link href="/admin" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-red-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-red-400">Admin Dashboard</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-red-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Main admin control panel</p>
              </Link>

              <Link href="/admin/users" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-red-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-red-400">User Management</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-red-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Manage platform users</p>
              </Link>

              <Link href="/admin/businesses" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-red-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-red-400">Business Management</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-red-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Manage business listings</p>
              </Link>

              <Link href="/admin/reviews" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-red-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-red-400">Review Management</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-red-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Moderate user reviews</p>
              </Link>

              <Link href="/admin/blog" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-red-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-red-400">Blog Management</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-red-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Manage blog content</p>
              </Link>

              <Link href="/admin/blog/new" className="bg-neutral-900 border border-neutral-800 rounded-xl p-4 hover:border-red-500 transition-colors group">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium group-hover:text-red-400">Create Blog Post</span>
                  <ExternalLink className="h-4 w-4 text-neutral-400 group-hover:text-red-400" />
                </div>
                <p className="text-sm text-neutral-400 mt-2">Write new blog article</p>
              </Link>

              <div className="bg-neutral-900 border border-neutral-700 rounded-xl p-4 opacity-75">
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300 font-medium">Edit Blog Posts</span>
                  <Search className="h-4 w-4 text-neutral-500" />
                </div>
                <p className="text-sm text-neutral-500 mt-2">Dynamic: /admin/blog/edit/[id]</p>
              </div>
            </div>
          </div>

          {/* Dynamic Routes Section */}
          <div className="mb-8">
            <div className="flex items-center mb-6">
              <Search className="h-6 w-6 text-cyan-400 mr-3" />
              <h2 className="text-2xl font-semibold text-white">Dynamic Routes</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <div className="bg-neutral-900 border border-neutral-700 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-neutral-300 font-medium">Business Profiles</span>
                  <Building2 className="h-4 w-4 text-neutral-500" />
                </div>
                <p className="text-sm text-neutral-500 mb-3">Individual business pages</p>
                <code className="text-xs bg-neutral-800 px-2 py-1 rounded text-cyan-400 block mb-2">
                  /business/[id]
                </code>
                <Link href="/business/elite-pressure-pros" className="text-blue-400 hover:text-blue-300 text-sm">
                  Example: Elite Pressure Pros →
                </Link>
              </div>

              <div className="bg-neutral-900 border border-neutral-700 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-neutral-300 font-medium">Blog Articles</span>
                  <FileText className="h-4 w-4 text-neutral-500" />
                </div>
                <p className="text-sm text-neutral-500 mb-3">Individual blog posts</p>
                <code className="text-xs bg-neutral-800 px-2 py-1 rounded text-cyan-400 block">
                  /blog/[id]
                </code>
              </div>

              <div className="bg-neutral-900 border border-neutral-700 rounded-xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-neutral-300 font-medium">Admin Blog Editor</span>
                  <Settings className="h-4 w-4 text-neutral-500" />
                </div>
                <p className="text-sm text-neutral-500 mb-3">Edit specific blog posts</p>
                <code className="text-xs bg-neutral-800 px-2 py-1 rounded text-cyan-400 block">
                  /admin/blog/edit/[id]
                </code>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center text-neutral-500 text-sm">
            <p>This directory contains all publicly accessible pages on PressureWash Pro.</p>
            <p className="mt-2">Some pages may require authentication or specific permissions to access.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
