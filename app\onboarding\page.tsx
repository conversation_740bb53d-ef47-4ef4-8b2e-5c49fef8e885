"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/hooks/use-user"
import { supabase } from "@/lib/supabase"
import { OnboardingFlow } from "@/components/onboarding/onboarding-flow"
import { Loader2 } from "lucide-react"

export default function OnboardingPage() {
  const router = useRouter()
  const { user, loading } = useUser()
  const [checkingBusiness, setCheckingBusiness] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      console.log("No user found, redirecting to login")
      router.push("/auth/login")
      return
    }

    if (user && supabase) {
      checkExistingBusiness()
    }
  }, [user, loading, router])

  const checkExistingBusiness = async () => {
    if (!user || !supabase) return

    try {
      setCheckingBusiness(true)
      const { data: business, error } = await supabase
        .from("businesses")
        .select("*")
        .eq("owner_id", user.id)
        .single()

      if (business) {
        console.log("User already has a business, redirecting to dashboard")
        router.push("/dashboard")
        return
      }

      if (error && error.code !== 'PGRST116') {
        console.error("Error checking business:", error)
      }
    } catch (error) {
      console.error("Business check error:", error)
    } finally {
      setCheckingBusiness(false)
    }
  }

  // Show loading while checking authentication and business status
  if (loading || checkingBusiness) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <p className="text-white">Loading onboarding...</p>
        </div>
      </div>
    )
  }

  // Don't render anything if no user (will redirect)
  if (!user) {
    return null
  }

  return <OnboardingFlow />
}
