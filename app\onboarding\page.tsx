import { redirect } from "next/navigation"
import { createServerClient } from "@/lib/supabase"
import { OnboardingFlow } from "@/components/onboarding/onboarding-flow"

export default async function OnboardingPage() {
  const supabase = createServerClient()

  // Handle case where Supabase is not configured (placeholder)
  if (!supabase) {
    return <OnboardingFlow />
  }

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      redirect("/auth/login")
    }

    // Check if user already has a business profile
    const { data: business } = await supabase.from("businesses").select("*").eq("owner_id", user.id).single()

    if (business) {
      redirect("/dashboard")
    }
  } catch (error) {
    // Handle Supabase errors gracefully
    console.error("Supabase error:", error)
  }

  return <OnboardingFlow />
}
