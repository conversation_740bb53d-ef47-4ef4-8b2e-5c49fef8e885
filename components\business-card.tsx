"use client"

import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import type { BusinessWithDetails } from "@/lib/types"
import { formatDistance } from "@/lib/geocoding"
import { Star, MapPin, Phone, ExternalLink, Crown, Navigation } from "lucide-react"

interface BusinessCardProps {
  business: BusinessWithDetails & { distance?: number }
}

export function BusinessCard({ business }: BusinessCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-neutral-600"}`}
      />
    ))
  }

  const primaryImage = business.gallery?.[0]?.image_url

  return (
    <div className="bg-neutral-900 border border-neutral-800 rounded-xl p-6 card-hover-blue">
      <div className="flex gap-4">
        {/* Business Image */}
        <div className="flex-shrink-0">
          <div className="w-24 h-24 bg-neutral-800 rounded-xl overflow-hidden">
            {primaryImage ? (
              <Image
                src={primaryImage || "/placeholder.svg"}
                alt={business.name}
                width={96}
                height={96}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="bg-blue-gradient p-2 rounded">
                  <MapPin className="h-6 w-6 text-white" />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Business Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-white truncate">{business.name}</h3>
              {business.is_premium && <Crown className="h-4 w-4 text-yellow-400 flex-shrink-0" />}
            </div>
          </div>

          {/* Rating */}
          <div className="flex items-center gap-2 mb-2">
            <div className="flex">{renderStars(business.avg_rating || 0)}</div>
            <span className="text-sm text-neutral-400">
              {business.avg_rating ? business.avg_rating.toFixed(1) : "No reviews"}
              {business.review_count ? ` (${business.review_count} reviews)` : ""}
            </span>
          </div>

          {/* Location and Distance */}
          <div className="flex items-center gap-4 mb-2">
            {(business.location?.city || business.location?.state) && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3 text-neutral-500" />
                <span className="text-sm text-neutral-400">
                  {business.location.city}
                  {business.location.city && business.location.state && ", "}
                  {business.location.state}
                </span>
              </div>
            )}
            {business.distance && (
              <div className="flex items-center gap-1">
                <Navigation className="h-3 w-3 text-green-500" />
                <span className="text-sm text-green-400">{formatDistance(business.distance)} away</span>
              </div>
            )}
          </div>

          {/* Services */}
          {business.services && business.services.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {business.services.slice(0, 3).map((service, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs"
                >
                  {service.name}
                </Badge>
              ))}
              {business.services.length > 3 && (
                <Badge variant="secondary" className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs">
                  +{business.services.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Description */}
          {business.description && <p className="text-sm text-neutral-400 mb-3 line-clamp-2">{business.description}</p>}

          {/* Contact Info */}
          <div className="flex items-center gap-4 mb-3 text-sm">
            {business.phone_number && (
              <div className="flex items-center gap-1">
                <Phone className="h-3 w-3 text-neutral-500" />
                <span className="text-neutral-400">{business.phone_number}</span>
              </div>
            )}
            {business.website_url && (
              <a
                href={business.website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-blue-400 hover:text-blue-300"
              >
                <ExternalLink className="h-3 w-3" />
                <span>Website</span>
              </a>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button asChild className="bg-blue-gradient-hover flex-1">
              <Link href={`/business/${business.id}`}>View Profile</Link>
            </Button>
            <Button variant="outline" className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent">
              Get Quote
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
