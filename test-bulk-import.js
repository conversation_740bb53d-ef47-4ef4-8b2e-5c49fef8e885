#!/usr/bin/env node

/**
 * Test script to verify bulk import functionality with Supabase
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testBulkImportAPI() {
  console.log('🧪 Testing Bulk Import API with Supabase')
  console.log('=====================================')

  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables')
    return false
  }

  console.log('✅ Environment variables configured')

  // Test database connection
  const supabase = createClient(supabaseUrl, supabaseServiceKey)

  try {
    // Test if we can access the services table
    const { data: services, error } = await supabase
      .from('services')
      .select('*')
      .limit(5)

    if (error) {
      console.error('❌ Database connection failed:', error.message)
      return false
    }

    console.log(`✅ Database connection successful - Found ${services.length} services`)

    // Test if we can access the businesses table
    const { data: businesses, error: businessError } = await supabase
      .from('businesses')
      .select('*')
      .limit(1)

    if (businessError) {
      console.error('❌ Businesses table access failed:', businessError.message)
      return false
    }

    console.log('✅ Businesses table accessible')

    // Test if we can access the locations table
    const { data: locations, error: locationError } = await supabase
      .from('locations')
      .select('*')
      .limit(1)

    if (locationError) {
      console.error('❌ Locations table access failed:', locationError.message)
      return false
    }

    console.log('✅ Locations table accessible')

    // Test if we can access the reviews table
    const { data: reviews, error: reviewError } = await supabase
      .from('reviews')
      .select('*')
      .limit(1)

    if (reviewError) {
      console.error('❌ Reviews table access failed:', reviewError.message)
      return false
    }

    console.log('✅ Reviews table accessible')

    // Test if we can access the business_services table
    const { data: businessServices, error: businessServiceError } = await supabase
      .from('business_services')
      .select('*')
      .limit(1)

    if (businessServiceError) {
      console.error('❌ Business services table access failed:', businessServiceError.message)
      return false
    }

    console.log('✅ Business services table accessible')

    console.log('\n🎉 All bulk import database requirements verified!')
    console.log('\n📋 Available Services for Import:')
    services.forEach((service, index) => {
      console.log(`   ${index + 1}. ${service.name}`)
    })

    console.log('\n✅ Bulk import is ready to use!')
    console.log('   Navigate to: http://localhost:3001/admin/bulk-import')

    return true

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    return false
  }
}

// Run the test
testBulkImportAPI()
  .then(success => {
    if (success) {
      console.log('\n🚀 Bulk import system is fully operational!')
    } else {
      console.log('\n⚠️  Some issues were found. Please check the errors above.')
    }
  })
  .catch(error => {
    console.error('❌ Test script failed:', error)
  })
