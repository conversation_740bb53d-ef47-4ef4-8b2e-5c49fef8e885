"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useUser } from "@/hooks/use-user"
import { Star, Send, Edit3 } from "lucide-react"
import { cn } from "@/lib/utils"
import type { Review } from "@/lib/types"

interface ReviewFormProps {
  businessSlug: string
  businessName: string
  existingReview?: Review
  onReviewSubmitted?: (review: Review) => void
  onCancel?: () => void
}

export function ReviewForm({ 
  businessSlug, 
  businessName, 
  existingReview, 
  onReviewSubmitted, 
  onCancel 
}: ReviewFormProps) {
  const { user } = useUser()
  const { toast } = useToast()
  const [rating, setRating] = useState(existingReview?.rating || 0)
  const [content, setContent] = useState(existingReview?.content || "")
  const [hoveredRating, setHoveredRating] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const isEditing = !!existingReview

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to leave a review.",
        variant: "destructive"
      })
      return
    }

    if (rating === 0) {
      toast({
        title: "Rating Required",
        description: "Please select a star rating.",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      const url = isEditing 
        ? `/api/reviews/${existingReview.id}`
        : `/api/businesses/${businessSlug}/reviews`
      
      const method = isEditing ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rating,
          content: content.trim() || undefined
        })
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 409) {
          toast({
            title: "Review Already Exists",
            description: data.error,
            variant: "destructive"
          })
        } else {
          throw new Error(data.error || 'Failed to submit review')
        }
        return
      }

      toast({
        title: isEditing ? "Review Updated" : "Review Submitted",
        description: isEditing 
          ? "Your review has been updated successfully."
          : "Thank you for your review! It helps other customers make informed decisions."
      })

      if (onReviewSubmitted) {
        onReviewSubmitted(data.review)
      }

      // Reset form if creating new review
      if (!isEditing) {
        setRating(0)
        setContent("")
      }
    } catch (error) {
      console.error('Error submitting review:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit review. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, index) => {
      const starValue = index + 1
      const isActive = starValue <= (hoveredRating || rating)
      
      return (
        <button
          key={index}
          type="button"
          className={cn(
            "transition-colors duration-150",
            isActive ? "text-yellow-400" : "text-neutral-600 hover:text-yellow-300"
          )}
          onMouseEnter={() => setHoveredRating(starValue)}
          onMouseLeave={() => setHoveredRating(0)}
          onClick={() => setRating(starValue)}
        >
          <Star className="h-8 w-8 fill-current" />
        </button>
      )
    })
  }

  if (!user) {
    return (
      <Card className="bg-neutral-900 border-neutral-800">
        <CardContent className="p-6 text-center">
          <p className="text-neutral-400 mb-4">Please sign in to leave a review for {businessName}</p>
          <Button asChild>
            <a href="/auth/signin">Sign In</a>
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          {isEditing ? (
            <>
              <Edit3 className="h-5 w-5 mr-2" />
              Edit Your Review
            </>
          ) : (
            <>
              <Star className="h-5 w-5 mr-2" />
              Write a Review for {businessName}
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Star Rating */}
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-3">
              Rating *
            </label>
            <div className="flex items-center space-x-1">
              {renderStars()}
              {rating > 0 && (
                <span className="ml-3 text-sm text-neutral-400">
                  {rating} star{rating !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          </div>

          {/* Review Content */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-neutral-300 mb-2">
              Your Review (Optional)
            </label>
            <Textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Share your experience with this business..."
              className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500 min-h-[120px]"
              maxLength={2000}
            />
            <p className="text-xs text-neutral-500 mt-1">
              {content.length}/2000 characters
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            <Button
              type="submit"
              disabled={rating === 0 || isSubmitting}
              className="bg-blue-gradient-hover"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditing ? 'Updating...' : 'Submitting...'}
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update Review' : 'Submit Review'}
                </>
              )}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
