"use client"

import { useState } from "react"
import { useUser } from "@/hooks/use-user"
import { WelcomeStep } from "./welcome-step"
import { BusinessBasicsStep } from "./business-basics-step"
import { SurveyStep } from "./survey-step"
import { CompletionStep } from "./completion-step"

export type OnboardingStep = 1 | 2 | 3 | 4

export interface OnboardingData {
  businessName: string
  phoneNumber: string
  city: string
  state: string
  zipCode: string
  challenges: string[]
  description: string
}

export function OnboardingFlow() {
  const { user } = useUser()
  const [currentStep, setCurrentStep] = useState<OnboardingStep>(1)
  const [data, setData] = useState<OnboardingData>({
    businessName: "",
    phoneNumber: "",
    city: "",
    state: "",
    zipCode: "",
    challenges: [],
    description: "",
  })

  const updateData = (newData: Partial<OnboardingData>) => {
    setData((prev) => ({ ...prev, ...newData }))
  }

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep((prev) => (prev + 1) as OnboardingStep)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => (prev - 1) as OnboardingStep)
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <WelcomeStep onNext={nextStep} userName={user?.user_metadata?.full_name || "there"} />
      case 2:
        return <BusinessBasicsStep data={data} updateData={updateData} onNext={nextStep} onBack={prevStep} />
      case 3:
        return <SurveyStep data={data} updateData={updateData} onNext={nextStep} onBack={prevStep} />
      case 4:
        return <CompletionStep data={data} />
      default:
        return <WelcomeStep onNext={nextStep} userName={user?.user_metadata?.full_name || "there"} />
    }
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="w-full px-6 py-8">
        {/* Progress Bar */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm text-neutral-400">Step {currentStep} of 4</span>
            <span className="text-sm text-neutral-400">{Math.round((currentStep / 4) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-neutral-800 rounded-full h-2">
            <div
              className="bg-blue-gradient h-2 rounded-full transition-all duration-300 glow-blue"
              style={{ width: `${(currentStep / 4) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        {renderStep()}
      </div>
    </div>
  )
}
