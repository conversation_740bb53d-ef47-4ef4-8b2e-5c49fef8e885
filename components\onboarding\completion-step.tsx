"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase"
import { useUser } from "@/hooks/use-user"
import { useToast } from "@/hooks/use-toast"
import type { OnboardingData } from "./onboarding-flow"
import { CheckCircle, ArrowRight, Crown, Camera, Star, TrendingUp, Loader2 } from "lucide-react"

interface CompletionStepProps {
  data: OnboardingData
}

export function CompletionStep({ data }: CompletionStepProps) {
  const { user } = useUser()
  const { toast } = useToast()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [businessCreated, setBusinessCreated] = useState(false)

  const createBusiness = async () => {
    if (!user || businessCreated) return

    setLoading(true)
    try {
      const { data: business, error } = await supabase
        .from("businesses")
        .insert({
          owner_id: user.id,
          name: data.businessName,
          description: data.description,
          phone_number: data.phoneNumber,
          city: data.city,
          state: data.state,
          zip_code: data.zipCode,
        })
        .select()
        .single()

      if (error) throw error

      setBusinessCreated(true)
      toast({
        title: "Business Profile Created!",
        description: "Your business is now live on PressureWash Pro.",
      })
    } catch (error) {
      console.error("Error creating business:", error)
      toast({
        title: "Error",
        description: "Failed to create business profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const goToDashboard = () => {
    if (!businessCreated) {
      createBusiness().then(() => {
        router.push("/dashboard")
      })
    } else {
      router.push("/dashboard")
    }
  }

  const upgradeToPremium = () => {
    // Frontend only - would integrate with Stripe
    console.log("Upgrade to premium clicked")
    toast({
      title: "Coming Soon!",
      description: "Premium upgrade functionality will be available soon.",
    })
  }

  const nextSteps = [
    {
      icon: Camera,
      title: "Add Photos",
      description: "Upload before/after photos to showcase your work",
    },
    {
      icon: Star,
      title: "Complete Profile",
      description: "Add services, description, and contact details",
    },
    {
      icon: TrendingUp,
      title: "Get Discovered",
      description: "Start receiving leads from potential customers",
    },
  ]

  return (
    <div className="max-w-4xl mx-auto text-center">
      {/* Success Icon */}
      <div className="mb-8">
        <div className="bg-green-500/10 border border-green-500/20 rounded-full w-24 h-24 mx-auto flex items-center justify-center mb-6">
          <CheckCircle className="h-12 w-12 text-green-400" />
        </div>
        <h1 className="text-4xl font-bold text-white mb-2">All Set!</h1>
        <h2 className="text-2xl font-semibold text-green-400 mb-4">Congratulations!</h2>
        <p className="text-xl text-neutral-400">Your business profile is now live and ready to attract customers.</p>
      </div>

      {/* Business Summary */}
      <Card className="bg-neutral-900 border-neutral-800 mb-8">
        <CardContent className="p-6">
          <h3 className="text-white font-semibold mb-4">Your Business Profile</h3>
          <div className="text-left space-y-2">
            <div className="flex justify-between">
              <span className="text-neutral-400">Business Name:</span>
              <span className="text-white">{data.businessName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-400">Location:</span>
              <span className="text-white">
                {data.city}, {data.state}
              </span>
            </div>
            {data.phoneNumber && (
              <div className="flex justify-between">
                <span className="text-neutral-400">Phone:</span>
                <span className="text-white">{data.phoneNumber}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* What's Next */}
      <div className="mb-8">
        <h3 className="text-2xl font-semibold text-white mb-6">What's next?</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          {nextSteps.map((step, index) => {
            const Icon = step.icon
            return (
              <Card key={index} className="bg-neutral-800 border-neutral-700">
                <CardContent className="p-4 text-center">
                  <div className="bg-blue-gradient p-2 rounded-full w-10 h-10 mx-auto mb-3 glow-blue">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="text-white font-medium mb-1">{step.title}</h4>
                  <p className="text-neutral-400 text-sm">{step.description}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-4">
        {/* Primary CTA */}
        <Button onClick={goToDashboard} disabled={loading} size="lg" className="w-full bg-blue-gradient-hover text-lg">
          {loading ? (
            <>
              <Loader2 className="h-5 w-5 mr-2 animate-spin" />
              Setting up your profile...
            </>
          ) : (
            <>
              Go to Dashboard
              <ArrowRight className="h-5 w-5 ml-2" />
            </>
          )}
        </Button>

        {/* Premium Upgrade CTA */}
        <Card className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-yellow-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-center gap-2 mb-3">
              <Crown className="h-5 w-5 text-yellow-400" />
              <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">POPULAR</Badge>
            </div>
            <h3 className="text-white font-semibold mb-2">Upgrade to Premium</h3>
            <p className="text-neutral-400 text-sm mb-4">
              Get featured placement, priority support, and 3x more visibility
            </p>
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-400">3x</div>
                <div className="text-xs text-neutral-400">More Leads</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-400">Featured</div>
                <div className="text-xs text-neutral-400">Placement</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-400">$29</div>
                <div className="text-xs text-neutral-400">/month</div>
              </div>
            </div>
            <Button
              onClick={upgradeToPremium}
              variant="outline"
              className="w-full border-yellow-500/20 text-yellow-400 hover:bg-yellow-500/10 bg-transparent"
            >
              <Crown className="h-4 w-4 mr-2" />
              Upgrade to Premium
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Skip Option */}
      <div className="mt-6">
        <button
          onClick={goToDashboard}
          className="text-neutral-400 hover:text-white text-sm transition-colors"
          disabled={loading}
        >
          I'll explore the dashboard first
        </button>
      </div>
    </div>
  )
}
