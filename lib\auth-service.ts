import { createServerClient, supabase } from './supabase'
import type { Profile } from './types'
import { z } from 'zod'

// Validation schemas
const signUpSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
})

const signInSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
})

const resetPasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Password confirmation is required'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export interface SignUpData {
  email: string
  password: string
  fullName: string
}

export interface SignInData {
  email: string
  password: string
}

export interface ResetPasswordData {
  password: string
  confirmPassword: string
}

export interface AuthResult {
  success: boolean
  user?: any
  profile?: Profile
  session?: any
  error?: string
  message?: string
  needsEmailConfirmation?: boolean
}

export class AuthService {
  private supabase: any
  private isServer: boolean

  constructor(isServer = false) {
    this.isServer = isServer
    if (!isServer) {
      this.supabase = supabase
    }
  }

  private async getSupabaseClient() {
    if (this.isServer) {
      return await createServerClient()
    }
    return this.supabase
  }

  async signUp(data: SignUpData): Promise<AuthResult> {
    try {
      // Validate input data
      const validationResult = signUpSchema.safeParse(data)
      if (!validationResult.success) {
        const firstError = validationResult.error.errors[0]
        return { success: false, error: firstError.message }
      }

      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return { success: false, error: 'Database connection not available' }
      }

      const { email, password, fullName } = validationResult.data

      const { data: authData, error } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
          emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/auth/callback`,
        },
      })

      if (error) {
        return { success: false, error: error.message }
      }

      // Check if user needs email confirmation
      if (authData.user && !authData.user.email_confirmed_at) {
        return {
          success: true,
          user: authData.user,
          needsEmailConfirmation: true,
          message: 'Please check your email and click the confirmation link to complete your registration.',
        }
      }

      // If user is confirmed, create profile automatically
      if (authData.user && authData.session) {
        try {
          const supabaseClient = await this.getSupabaseClient()
          if (supabaseClient) {
            // Check if profile already exists
            const { data: existingProfile } = await supabaseClient
              .from('profiles')
              .select('id')
              .eq('id', authData.user.id)
              .single()

            // Create profile if it doesn't exist
            if (!existingProfile) {
              await supabaseClient
                .from('profiles')
                .insert({
                  id: authData.user.id,
                  full_name: fullName,
                  updated_at: new Date().toISOString(),
                })
            }
          }
        } catch (profileError) {
          console.error('Profile creation error during signup:', profileError)
          // Continue anyway - profile can be created later
        }
      }

      return {
        success: true,
        user: authData.user,
        session: authData.session,
        message: 'Account created successfully!',
      }
    } catch (error) {
      console.error('SignUp error:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async signIn(data: SignInData): Promise<AuthResult> {
    try {
      // Validate input data
      const validationResult = signInSchema.safeParse(data)
      if (!validationResult.success) {
        const firstError = validationResult.error.errors[0]
        return { success: false, error: firstError.message }
      }

      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return { success: false, error: 'Database connection not available' }
      }

      const { email, password } = validationResult.data

      const { data: authData, error } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        let errorMessage = error.message
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password'
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'Please check your email and confirm your account before signing in'
        }
        return { success: false, error: errorMessage }
      }

      if (!authData.user) {
        return { success: false, error: 'Authentication failed' }
      }

      // Get user profile
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single()

      return {
        success: true,
        user: authData.user,
        profile,
        session: authData.session,
        message: 'Signed in successfully!',
      }
    } catch (error) {
      console.error('SignIn error:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async signOut(): Promise<AuthResult> {
    try {
      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return { success: false, error: 'Database connection not available' }
      }

      const { error } = await supabaseClient.auth.signOut()

      if (error) {
        return { success: false, error: error.message }
      }

      return {
        success: true,
        message: 'Signed out successfully!',
      }
    } catch (error) {
      console.error('SignOut error:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async forgotPassword(email: string): Promise<AuthResult> {
    try {
      // Validate input data
      const validationResult = forgotPasswordSchema.safeParse({ email })
      if (!validationResult.success) {
        const firstError = validationResult.error.errors[0]
        return { success: false, error: firstError.message }
      }

      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return { success: false, error: 'Database connection not available' }
      }

      const { error } = await supabaseClient.auth.resetPasswordForEmail(validationResult.data.email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/auth/reset-password`,
      })

      if (error) {
        return { success: false, error: error.message }
      }

      // Always return success to prevent email enumeration attacks
      return {
        success: true,
        message: 'If an account with that email exists, we have sent a password reset link.',
      }
    } catch (error) {
      console.error('ForgotPassword error:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async resetPassword(data: ResetPasswordData): Promise<AuthResult> {
    try {
      // Validate input data
      const validationResult = resetPasswordSchema.safeParse(data)
      if (!validationResult.success) {
        const firstError = validationResult.error.errors[0]
        return { success: false, error: firstError.message }
      }

      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return { success: false, error: 'Database connection not available' }
      }

      const { password } = validationResult.data

      const { data: authData, error } = await supabaseClient.auth.updateUser({
        password: password,
      })

      if (error) {
        return { success: false, error: error.message }
      }

      if (!authData.user) {
        return { success: false, error: 'User not found or session expired' }
      }

      return {
        success: true,
        user: authData.user,
        message: 'Password updated successfully!',
      }
    } catch (error) {
      console.error('ResetPassword error:', error)
      return { success: false, error: 'An unexpected error occurred' }
    }
  }

  async getCurrentUser() {
    try {
      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return null
      }

      const { data: { user }, error } = await supabaseClient.auth.getUser()

      if (error || !user) {
        return null
      }

      return user
    } catch (error) {
      console.error('GetCurrentUser error:', error)
      return null
    }
  }

  async getCurrentSession() {
    try {
      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return null
      }

      const { data: { session }, error } = await supabaseClient.auth.getSession()

      if (error || !session) {
        return null
      }

      return session
    } catch (error) {
      console.error('GetCurrentSession error:', error)
      return null
    }
  }

  async refreshSession() {
    try {
      const supabaseClient = await this.getSupabaseClient()
      if (!supabaseClient) {
        return null
      }

      const { data, error } = await supabaseClient.auth.refreshSession()

      if (error) {
        console.error('RefreshSession error:', error)
        return null
      }

      return data.session
    } catch (error) {
      console.error('RefreshSession error:', error)
      return null
    }
  }
}