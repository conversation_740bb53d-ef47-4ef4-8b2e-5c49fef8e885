import { Header } from "@/components/header"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Target, Users, Shield, Heart, Zap } from "lucide-react"

export default function AboutPage() {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      bio: "Former contractor with 15+ years in the pressure washing industry. Passionate about connecting homeowners with quality service providers.",
      image: "/placeholder.svg?height=300&width=300",
    },
    {
      name: "<PERSON>",
      role: "Co-Founder & CTO",
      bio: "Tech entrepreneur with expertise in marketplace platforms. Previously built and sold two successful service directories.",
      image: "/placeholder.svg?height=300&width=300",
    },
    {
      name: "<PERSON>",
      role: "Head of Operations",
      bio: "Operations expert focused on ensuring quality standards and customer satisfaction across our platform.",
      image: "/placeholder.svg?height=300&width=300",
    },
  ]

  const values = [
    {
      icon: Shield,
      title: "Trust & Safety",
      description: "Every business is verified and reviewed to ensure quality service for homeowners.",
    },
    {
      icon: Heart,
      title: "Customer First",
      description: "We prioritize customer satisfaction and work to resolve any issues quickly.",
    },
    {
      icon: Zap,
      title: "Innovation",
      description: "Constantly improving our platform with new features and better user experiences.",
    },
  ]

  return (
    <div className="min-h-screen bg-black">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            About <span className="bg-blue-gradient bg-clip-text text-white">PressureWash Pro</span>
          </h1>
          <p className="text-xl text-neutral-400 mb-8 max-w-3xl mx-auto">
            We're building the most trusted platform for connecting homeowners with professional pressure washing
            services across the country.
          </p>

          {/* Hero Image */}
          <div className="max-w-4xl mx-auto">
            <div className="relative h-64 md:h-96 bg-neutral-900 border border-neutral-800 rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=400&width=800"
                alt="Professional pressure washing team at work"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center gap-3">
                <Target className="h-8 w-8 text-blue-400" />
                Our Mission
              </h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-semibold text-white mb-4">Connecting Quality with Convenience</h3>
                <p className="text-neutral-400 mb-6 leading-relaxed">
                  We are dedicated to connecting homeowners with the best, most reliable pressure washing professionals
                  in their area. Finding a trusted contractor should be simple, and that's the problem we solve.
                </p>
                <p className="text-neutral-400 mb-6 leading-relaxed">
                  Our platform eliminates the guesswork by providing verified reviews, detailed business profiles, and
                  easy quote requests. We believe every homeowner deserves access to quality service providers, and
                  every business deserves the opportunity to grow.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">10,000+</div>
                    <div className="text-neutral-400 text-sm">Happy Customers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">500+</div>
                    <div className="text-neutral-400 text-sm">Verified Businesses</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">50+</div>
                    <div className="text-neutral-400 text-sm">Cities Served</div>
                  </div>
                </div>
              </div>

              <div className="relative">
                <div className="h-80 bg-neutral-900 border border-neutral-800 rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=320&width=400"
                    alt="Before and after pressure washing results"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 px-4 bg-neutral-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Our Values</h2>
            <p className="text-neutral-400 max-w-2xl mx-auto">
              These core principles guide everything we do and shape how we serve our community.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <Card key={index} className="bg-neutral-900 border-neutral-800 card-hover-blue">
                  <CardContent className="p-6 text-center">
                    <div className="bg-blue-gradient p-4 rounded-full w-16 h-16 mx-auto mb-4 glow-blue">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-3">{value.title}</h3>
                    <p className="text-neutral-400">{value.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center gap-3">
              <Users className="h-8 w-8 text-blue-400" />
              Meet the Team
            </h2>
            <p className="text-neutral-400 max-w-2xl mx-auto">
              Our passionate team is dedicated to revolutionizing how homeowners find and connect with pressure washing
              professionals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="bg-neutral-900 border-neutral-800 card-hover-blue">
                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <div className="relative w-32 h-32 mx-auto mb-4">
                      <Image
                        src={member.image || "/placeholder.svg"}
                        alt={member.name}
                        fill
                        className="object-cover rounded-full"
                      />
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-1">{member.name}</h3>
                    <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20 mb-3">{member.role}</Badge>
                  </div>
                  <p className="text-neutral-400 text-sm leading-relaxed">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
        <div className="container mx-auto text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-neutral-400 mb-8">
              Whether you're a homeowner looking for pressure washing services or a business owner wanting to grow your
              customer base, we're here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/search"
                className="px-8 py-3 bg-blue-gradient-hover text-white font-medium rounded-lg text-center"
              >
                Find Services
              </a>
              <a
                href="/auth/signup"
                className="px-8 py-3 border border-blue-500/20 text-blue-400 hover:bg-blue-500/10 font-medium rounded-lg text-center transition-colors"
              >
                List Your Business
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">PressureWash Pro</h3>
              <p className="text-neutral-400">The premier directory for pressure washing services.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Customers</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/search" className="hover:text-white transition-colors">
                    Find Services
                  </a>
                </li>
                <li>
                  <a href="/how-it-works" className="hover:text-white transition-colors">
                    How It Works
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Businesses</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/auth/signup" className="hover:text-white transition-colors">
                    List Your Business
                  </a>
                </li>
                <li>
                  <a href="/dashboard" className="hover:text-white transition-colors">
                    Business Dashboard
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 PressureWash Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
