// Geocoding utilities for location-based search

export interface Coordinates {
  lat: number
  lng: number
}

export interface GeocodingResult {
  coordinates: Coordinates
  formattedAddress: string
  city?: string
  state?: string
  zipCode?: string
}

// Simple city coordinates lookup (in production, use a geocoding service like Google Maps API)
const CITY_COORDINATES: Record<string, Coordinates> = {
  // Arizona
  'Phoenix,AZ': { lat: 33.4484, lng: -112.0740 },
  'Scottsdale,AZ': { lat: 33.4942, lng: -111.9261 },
  'Tempe,AZ': { lat: 33.4255, lng: -111.9400 },
  'Mesa,AZ': { lat: 33.4152, lng: -111.8315 },
  'Chandler,AZ': { lat: 33.3062, lng: -111.8413 },
  'Glendale,AZ': { lat: 33.5387, lng: -112.1860 },
  'Tucson,AZ': { lat: 32.2226, lng: -110.9747 },
  
  // California
  'Los Angeles,CA': { lat: 34.0522, lng: -118.2437 },
  'San Diego,CA': { lat: 32.7157, lng: -117.1611 },
  'San Francisco,CA': { lat: 37.7749, lng: -122.4194 },
  'Sacramento,CA': { lat: 38.5816, lng: -121.4944 },
  'San Jose,CA': { lat: 37.3382, lng: -121.8863 },
  'Fresno,CA': { lat: 36.7378, lng: -119.7871 },
  
  // Texas
  'Austin,TX': { lat: 30.2672, lng: -97.7431 },
  'Dallas,TX': { lat: 32.7767, lng: -96.7970 },
  'Houston,TX': { lat: 29.7604, lng: -95.3698 },
  'San Antonio,TX': { lat: 29.4241, lng: -98.4936 },
  'Fort Worth,TX': { lat: 32.7555, lng: -97.3308 },
  
  // Florida
  'Miami,FL': { lat: 25.7617, lng: -80.1918 },
  'Orlando,FL': { lat: 28.5383, lng: -81.3792 },
  'Tampa,FL': { lat: 27.9506, lng: -82.4572 },
  'Jacksonville,FL': { lat: 30.3322, lng: -81.6557 },
  'Fort Lauderdale,FL': { lat: 26.1224, lng: -80.1373 },
  
  // Other major cities
  'Atlanta,GA': { lat: 33.7490, lng: -84.3880 },
  'Denver,CO': { lat: 39.7392, lng: -104.9903 },
  'Seattle,WA': { lat: 47.6062, lng: -122.3321 },
  'Portland,OR': { lat: 45.5152, lng: -122.6784 },
  'Las Vegas,NV': { lat: 36.1699, lng: -115.1398 },
  'Chicago,IL': { lat: 41.8781, lng: -87.6298 },
  'New York,NY': { lat: 40.7128, lng: -74.0060 },
  'Boston,MA': { lat: 42.3601, lng: -71.0589 },
  'Philadelphia,PA': { lat: 39.9526, lng: -75.1652 },
  'Nashville,TN': { lat: 36.1627, lng: -86.7816 },
  'Charlotte,NC': { lat: 35.2271, lng: -80.8431 },
  'Raleigh,NC': { lat: 35.7796, lng: -78.6382 }
}

// ZIP code to coordinates lookup (sample data - in production use a comprehensive database)
const ZIP_COORDINATES: Record<string, Coordinates> = {
  // Arizona ZIP codes
  '85001': { lat: 33.4484, lng: -112.0740 }, // Phoenix
  '85002': { lat: 33.4734, lng: -112.0596 }, // Phoenix
  '85003': { lat: 33.4734, lng: -112.0596 }, // Phoenix
  '85251': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
  '85281': { lat: 33.4255, lng: -111.9400 }, // Tempe
  '85201': { lat: 33.4152, lng: -111.8315 }, // Mesa
  '85224': { lat: 33.3062, lng: -111.8413 }, // Chandler
  
  // California ZIP codes
  '90210': { lat: 34.0901, lng: -118.4065 }, // Beverly Hills
  '90401': { lat: 34.0195, lng: -118.4912 }, // Santa Monica
  '92101': { lat: 32.7157, lng: -117.1611 }, // San Diego
  '94102': { lat: 37.7749, lng: -122.4194 }, // San Francisco
  
  // Texas ZIP codes
  '78701': { lat: 30.2672, lng: -97.7431 }, // Austin
  '75201': { lat: 32.7767, lng: -96.7970 }, // Dallas
  '77002': { lat: 29.7604, lng: -95.3698 }, // Houston
  
  // Florida ZIP codes
  '33101': { lat: 25.7617, lng: -80.1918 }, // Miami
  '32801': { lat: 28.5383, lng: -81.3792 }, // Orlando
  '33602': { lat: 27.9506, lng: -82.4572 }  // Tampa
}

/**
 * Get coordinates for a city and state combination
 */
export function getCityCoordinates(city?: string, state?: string): Coordinates | null {
  if (!city || !state) return null
  
  const key = `${city},${state}`
  return CITY_COORDINATES[key] || null
}

/**
 * Get coordinates for a ZIP code
 */
export function getZipCoordinates(zipCode: string): Coordinates | null {
  return ZIP_COORDINATES[zipCode] || null
}

/**
 * Calculate distance between two points using Haversine formula
 * Returns distance in miles
 */
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3959 // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

/**
 * Geocode an address string to coordinates
 * In production, this would call a geocoding API like Google Maps
 */
export async function geocodeAddress(address: string): Promise<GeocodingResult | null> {
  // Simple parsing for demo purposes
  const parts = address.split(',').map(part => part.trim())
  
  if (parts.length >= 2) {
    const city = parts[0]
    const stateOrZip = parts[1]
    
    // Check if it's a ZIP code
    if (/^\d{5}$/.test(stateOrZip)) {
      const coordinates = getZipCoordinates(stateOrZip)
      if (coordinates) {
        return {
          coordinates,
          formattedAddress: address,
          zipCode: stateOrZip
        }
      }
    }
    
    // Check if it's city, state
    const coordinates = getCityCoordinates(city, stateOrZip)
    if (coordinates) {
      return {
        coordinates,
        formattedAddress: address,
        city,
        state: stateOrZip
      }
    }
  }
  
  return null
}

/**
 * Get business coordinates from location data
 */
export function getBusinessCoordinates(location?: {
  city?: string
  state?: string
  zip_code?: string
}): Coordinates | null {
  if (!location) return null
  
  // Try ZIP code first (more precise)
  if (location.zip_code) {
    const zipCoords = getZipCoordinates(location.zip_code)
    if (zipCoords) return zipCoords
  }
  
  // Fall back to city coordinates
  if (location.city && location.state) {
    return getCityCoordinates(location.city, location.state)
  }
  
  return null
}

/**
 * Format distance for display
 */
export function formatDistance(distance: number): string {
  if (distance < 0.1) {
    return '< 0.1 mi'
  } else if (distance < 1) {
    return `${distance.toFixed(1)} mi`
  } else if (distance < 10) {
    return `${distance.toFixed(1)} mi`
  } else {
    return `${Math.round(distance)} mi`
  }
}

/**
 * Sort businesses by distance
 */
export function sortByDistance<T extends { distance?: number }>(
  items: T[], 
  ascending: boolean = true
): T[] {
  return items.sort((a, b) => {
    const distA = a.distance ?? Infinity
    const distB = b.distance ?? Infinity
    return ascending ? distA - distB : distB - distA
  })
}

/**
 * Filter businesses within radius
 */
export function filterByRadius<T extends { distance?: number }>(
  items: T[], 
  radius: number
): T[] {
  return items.filter(item => 
    item.distance === undefined || item.distance <= radius
  )
}