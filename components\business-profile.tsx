'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useUser } from '@/hooks/use-user'
import { useToast } from '@/hooks/use-toast'
import type { BusinessWithDetails, ReviewWithProfile, Review } from '@/lib/types'
import { ReviewForm } from './review-form'
import { ReviewCard } from './review-card'
import {
  Star,
  MapPin,
  Phone,
  Globe,
  MessageSquare,
  StarIcon,
  Calendar,
  User,
  Send,
  Plus
} from 'lucide-react'

interface BusinessProfileProps {
  business: BusinessWithDetails
  initialReviews: ReviewWithProfile[]
}

export function BusinessProfile({ business, initialReviews }: BusinessProfileProps) {
  const { user } = useUser()
  const { toast } = useToast()
  const router = useRouter()
  const [reviews, setReviews] = useState(initialReviews)
  const [showQuoteDialog, setShowQuoteDialog] = useState(false)
  const [showReviewDialog, setShowReviewDialog] = useState(false)
  const [quoteForm, setQuoteForm] = useState({
    subject: '',
    message: ''
  })
  const [reviewForm, setReviewForm] = useState({
    rating: 5,
    content: ''
  })
  const [loading, setLoading] = useState(false)

  // Check if user has already reviewed this business
  const userReview = reviews.find(review => review.author_id === user?.id)
  const hasUserReviewed = !!userReview

  const handleReviewSubmitted = (newReview: Review) => {
    setReviews(prev => [newReview as ReviewWithProfile, ...prev])
    setShowReviewDialog(false)
  }

  const handleReviewUpdated = (updatedReview: Review) => {
    setReviews(prev => prev.map(review =>
      review.id === updatedReview.id ? updatedReview as ReviewWithProfile : review
    ))
  }

  const handleReviewDeleted = (reviewId: string) => {
    setReviews(prev => prev.filter(review => review.id !== reviewId))
  }

  const handleQuoteRequest = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to request a quote.',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    try {
      // Create message thread
      const response = await fetch('/api/messages/threads', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessId: business.id,
          subject: quoteForm.subject || 'Quote Request'
        })
      })

      if (!response.ok) throw new Error('Failed to create thread')

      const { thread } = await response.json()

      // Send initial message
      await fetch(`/api/messages/${thread.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: quoteForm.message
        })
      })

      toast({
        title: 'Quote Request Sent!',
        description: 'The business will respond to your request soon.'
      })

      setShowQuoteDialog(false)
      setQuoteForm({ subject: '', message: '' })

      // Redirect to messages page
      router.push('/messages')
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send quote request. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleReviewSubmit = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to leave a review.',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/businesses/${business.slug}/reviews`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reviewForm)
      })

      if (!response.ok) throw new Error('Failed to submit review')

      const { review } = await response.json()
      setReviews(prev => [review, ...prev])

      toast({
        title: 'Review Submitted!',
        description: 'Thank you for your feedback.'
      })

      setShowReviewDialog(false)
      setReviewForm({ rating: 5, content: '' })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit review. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => interactive && onRatingChange?.(star)}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
            disabled={!interactive}
          >
            <StarIcon
              className={`h-5 w-5 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-neutral-600'
              }`}
            />
          </button>
        ))}
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Business Header */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Business Info */}
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-white mb-2">{business.name}</h1>
            
            {/* Rating */}
            <div className="flex items-center gap-2 mb-4">
              {renderStars(Math.round(business.avg_rating))}
              <span className="text-white font-medium">{business.avg_rating.toFixed(1)}</span>
              <span className="text-neutral-400">({business.review_count} reviews)</span>
            </div>

            {/* Location */}
            {business.location && (
              <div className="flex items-center gap-2 text-neutral-400 mb-2">
                <MapPin className="h-4 w-4" />
                <span>
                  {business.location.city}, {business.location.state} {business.location.zip_code}
                </span>
              </div>
            )}

            {/* Contact Info */}
            <div className="flex flex-wrap gap-4 mb-4">
              {business.phone && (
                <div className="flex items-center gap-2 text-neutral-400">
                  <Phone className="h-4 w-4" />
                  <span>{business.phone}</span>
                </div>
              )}
              {business.website_url && (
                <div className="flex items-center gap-2 text-neutral-400">
                  <Globe className="h-4 w-4" />
                  <a 
                    href={business.website_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="hover:text-blue-400 transition-colors"
                  >
                    Visit Website
                  </a>
                </div>
              )}
            </div>

            {/* Services */}
            {business.services && business.services.length > 0 && (
              <div className="mb-4">
                <h3 className="text-white font-medium mb-2">Services</h3>
                <div className="flex flex-wrap gap-2">
                  {business.services.map((service) => (
                    <Badge 
                      key={service.id} 
                      className="bg-neutral-800 text-neutral-400 border-neutral-700"
                    >
                      {service.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="lg:w-80">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Dialog open={showQuoteDialog} onOpenChange={setShowQuoteDialog}>
                    <DialogTrigger asChild>
                      <Button className="w-full bg-blue-gradient-hover">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Request Quote
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-neutral-900 border-neutral-800">
                      <DialogHeader>
                        <DialogTitle className="text-white">Request a Quote</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="subject" className="text-white">Subject</Label>
                          <Input
                            id="subject"
                            value={quoteForm.subject}
                            onChange={(e) => setQuoteForm(prev => ({ ...prev, subject: e.target.value }))}
                            placeholder="e.g., House washing quote"
                            className="bg-neutral-800 border-neutral-700 text-white"
                          />
                        </div>
                        <div>
                          <Label htmlFor="message" className="text-white">Message</Label>
                          <Textarea
                            id="message"
                            value={quoteForm.message}
                            onChange={(e) => setQuoteForm(prev => ({ ...prev, message: e.target.value }))}
                            placeholder="Please describe your project..."
                            className="bg-neutral-800 border-neutral-700 text-white"
                            rows={4}
                          />
                        </div>
                        <Button 
                          onClick={handleQuoteRequest} 
                          disabled={loading || !quoteForm.message.trim()}
                          className="w-full bg-blue-gradient-hover"
                        >
                          <Send className="h-4 w-4 mr-2" />
                          {loading ? 'Sending...' : 'Send Request'}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>

                  <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="w-full border-neutral-700 text-neutral-300 hover:bg-neutral-800">
                        <Star className="h-4 w-4 mr-2" />
                        Write Review
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-neutral-900 border-neutral-800">
                      <DialogHeader>
                        <DialogTitle className="text-white">Write a Review</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-white">Rating</Label>
                          <div className="mt-2">
                            {renderStars(reviewForm.rating, true, (rating) => 
                              setReviewForm(prev => ({ ...prev, rating }))
                            )}
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="review-content" className="text-white">Review</Label>
                          <Textarea
                            id="review-content"
                            value={reviewForm.content}
                            onChange={(e) => setReviewForm(prev => ({ ...prev, content: e.target.value }))}
                            placeholder="Share your experience..."
                            className="bg-neutral-800 border-neutral-700 text-white"
                            rows={4}
                          />
                        </div>
                        <Button 
                          onClick={handleReviewSubmit} 
                          disabled={loading}
                          className="w-full bg-blue-gradient-hover"
                        >
                          {loading ? 'Submitting...' : 'Submit Review'}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Business Details Tabs */}
      <Tabs defaultValue="about" className="w-full">
        <TabsList className="bg-neutral-900 border-blue-500/20">
          <TabsTrigger 
            value="about"
            className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400"
          >
            About
          </TabsTrigger>
          <TabsTrigger 
            value="gallery"
            className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400"
          >
            Gallery
          </TabsTrigger>
          <TabsTrigger 
            value="reviews"
            className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400"
          >
            Reviews ({business.review_count})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="about" className="mt-6">
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">About {business.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-300 leading-relaxed">
                {business.description || 'No description available.'}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gallery" className="mt-6">
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Photo Gallery</CardTitle>
            </CardHeader>
            <CardContent>
              {business.portfolio_images && business.portfolio_images.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {business.portfolio_images.map((image) => (
                    <div key={image.id} className="aspect-square bg-neutral-800 rounded-lg overflow-hidden">
                      <img
                        src={image.image_url}
                        alt={image.caption || 'Portfolio image'}
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-neutral-400 text-center py-8">No photos available yet.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="mt-6">
          <div className="space-y-6">
            {/* Review Form or Add Review Button */}
            {user && !hasUserReviewed && (
              <ReviewForm
                businessSlug={business.slug}
                businessName={business.name}
                onReviewSubmitted={handleReviewSubmitted}
              />
            )}

            {user && hasUserReviewed && (
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <p className="text-neutral-300">You have already reviewed this business.</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowReviewDialog(true)}
                      className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Edit Review
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {!user && (
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6 text-center">
                  <p className="text-neutral-400 mb-4">Sign in to leave a review for {business.name}</p>
                  <Button asChild>
                    <a href="/auth/signin">Sign In</a>
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Reviews List */}
            <Card className="bg-neutral-900 border-neutral-800">
              <CardHeader>
                <CardTitle className="text-white">
                  Customer Reviews ({reviews.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {reviews.length > 0 ? (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                        businessSlug={business.slug}
                        businessName={business.name}
                        onReviewUpdated={handleReviewUpdated}
                        onReviewDeleted={handleReviewDeleted}
                      />
                    ))}
                  </div>
                ) : (
                  <p className="text-neutral-400 text-center py-8">
                    No reviews yet. Be the first to leave a review!
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}