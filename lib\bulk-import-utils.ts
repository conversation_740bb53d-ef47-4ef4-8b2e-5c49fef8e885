// Utility functions for bulk import processing

export interface ImportedBusiness {
  name: string
  address: string
  rating: number
  totalRatings: number
  latitude: number
  longitude: number
  phoneNumber?: string
  website?: string
  businessStatus?: string
  placeId?: string
  types?: string[]
  reviews?: ImportedReview[]
  weekdayText?: string[]
}

export interface ImportedReview {
  author_name: string
  rating: number
  text: string
  time: number
  relative_time_description?: string
}

export interface ProcessedBusiness {
  business: {
    name: string
    slug: string
    description: string
    phone?: string
    website_url?: string
    avg_rating: number
    review_count: number
    owner_id: string
    google_place_id?: string
    google_maps_url?: string
    business_status: string
  }
  location: {
    street_address: string
    city: string
    state: string
    zip_code: string
    latitude: number
    longitude: number
  }
  services: number[]
  reviews: Array<{
    rating: number
    title: string
    content: string
    created_at: string
  }>
}

/**
 * Parse address string into components
 */
export function parseAddress(address: string) {
  const parts = address.split(',').map(part => part.trim())
  
  if (parts.length >= 3) {
    const streetAddress = parts[0]
    const city = parts[1]
    const stateZip = parts[2].split(' ')
    const state = stateZip[0]
    const zipCode = stateZip[1] || ''
    
    return {
      street_address: streetAddress,
      city,
      state,
      zip_code: zipCode
    }
  }
  
  return {
    street_address: address,
    city: '',
    state: '',
    zip_code: ''
  }
}

/**
 * Generate URL-friendly slug from business name
 */
export function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

/**
 * Extract service IDs based on business name and types
 */
export function extractServices(name: string, types?: string[]): number[] {
  const serviceKeywords = {
    1: ['house', 'home', 'residential', 'siding', 'exterior', 'vinyl'],
    2: ['driveway', 'concrete', 'pavement', 'sidewalk', 'walkway'],
    3: ['deck', 'patio', 'wood', 'composite', 'fence', 'pergola'],
    4: ['commercial', 'business', 'industrial', 'office', 'building']
  }
  
  const nameAndTypes = [name.toLowerCase(), ...(types || []).map(t => t.toLowerCase())]
  const services: number[] = []
  
  Object.entries(serviceKeywords).forEach(([serviceId, keywords]) => {
    if (keywords.some(keyword => 
      nameAndTypes.some(text => text.includes(keyword))
    )) {
      services.push(parseInt(serviceId))
    }
  })
  
  // Default to house washing if no specific services found
  if (services.length === 0) {
    services.push(1)
  }
  
  return services
}

/**
 * Clean and format phone number
 */
export function formatPhoneNumber(phone?: string): string | null {
  if (!phone) return null
  
  // Remove all non-digit characters
  const digits = phone.replace(/[^\d]/g, '')
  
  // Return last 10 digits if available
  if (digits.length >= 10) {
    return digits.slice(-10)
  }
  
  return null
}

/**
 * Validate and clean website URL
 */
export function formatWebsiteUrl(website?: string): string | null {
  if (!website || website === '') return null
  
  // Add protocol if missing
  if (!website.startsWith('http://') && !website.startsWith('https://')) {
    website = 'https://' + website
  }
  
  try {
    new URL(website)
    return website
  } catch {
    return null
  }
}

/**
 * Generate business description from location and services
 */
export function generateDescription(name: string, city: string, state: string, services: number[]): string {
  const serviceNames = {
    1: 'house washing',
    2: 'driveway cleaning',
    3: 'deck and patio cleaning',
    4: 'commercial pressure washing'
  }
  
  const serviceList = services.map(id => serviceNames[id as keyof typeof serviceNames]).filter(Boolean)
  const location = city && state ? `${city}, ${state}` : 'your area'
  
  if (serviceList.length > 0) {
    return `Professional ${serviceList.join(', ')} services in ${location}. ${name} provides high-quality pressure washing solutions for residential and commercial properties.`
  }
  
  return `Professional pressure washing services in ${location}. ${name} provides high-quality cleaning solutions for residential and commercial properties.`
}

/**
 * Process imported business data into database-ready format
 */
export function processBusinessData(business: ImportedBusiness, ownerId: string): ProcessedBusiness {
  const slug = generateSlug(business.name)
  const addressParts = parseAddress(business.address)
  const services = extractServices(business.name, business.types)
  const phone = formatPhoneNumber(business.phoneNumber)
  const website = formatWebsiteUrl(business.website)
  const description = generateDescription(business.name, addressParts.city, addressParts.state, services)

  // Process reviews
  const reviews = (business.reviews || []).slice(0, 10).map(review => ({
    rating: review.rating,
    content: review.text,
    created_at: new Date(review.time * 1000).toISOString()
  }))

  return {
    business: {
      name: business.name,
      slug,
      description,
      phone,
      website_url: website,
      avg_rating: business.rating,
      review_count: business.totalRatings,
      owner_id: ownerId
    },
    location: {
      street_address: addressParts.street_address,
      city: addressParts.city,
      state: addressParts.state,
      zip_code: addressParts.zip_code
    },
    services,
    reviews
  }
}

/**
 * Validate required business fields
 */
export function validateBusinessData(business: any): string[] {
  const errors: string[] = []
  
  if (!business.name || typeof business.name !== 'string') {
    errors.push('Business name is required')
  }
  
  if (!business.address || typeof business.address !== 'string') {
    errors.push('Business address is required')
  }
  
  if (typeof business.latitude !== 'number' || isNaN(business.latitude)) {
    errors.push('Valid latitude is required')
  }
  
  if (typeof business.longitude !== 'number' || isNaN(business.longitude)) {
    errors.push('Valid longitude is required')
  }
  
  if (typeof business.rating !== 'number' || business.rating < 0 || business.rating > 5) {
    errors.push('Rating must be a number between 0 and 5')
  }
  
  if (typeof business.totalRatings !== 'number' || business.totalRatings < 0) {
    errors.push('Total ratings must be a non-negative number')
  }
  
  return errors
}

/**
 * Parse CSV content into business objects with proper column mapping
 */
export function parseCSV(csvContent: string): ImportedBusiness[] {
  const lines = csvContent.trim().split('\n')
  if (lines.length < 2) {
    throw new Error('CSV must have at least a header row and one data row')
  }

  // Parse CSV with proper handling of quoted fields containing commas
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = []
    let current = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === '\t' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }

    result.push(current.trim())
    return result
  }

  const headers = parseCSVLine(lines[0])
  const businesses: ImportedBusiness[] = []

  // Create column mapping for the specific CSV format
  const columnMapping: { [key: string]: string } = {
    'Name': 'name',
    'Address': 'address',
    'Phone Number': 'phoneNumber',
    'International Phone': 'internationalPhoneNumber',
    'Website': 'website',
    'Google Maps URL': 'googleMapsUrl',
    'Rating': 'rating',
    'Total Ratings': 'totalRatings',
    'Status': 'status',
    'Business Status': 'businessStatus',
    'Price Level': 'priceLevel',
    'Price Level Text': 'priceLevelText',
    'Latitude': 'latitude',
    'Longitude': 'longitude',
    'Plus Code': 'plusCode',
    'Place ID': 'placeId',
    'Types': 'types',
    'Hours': 'weekdayText',
    'Photo URLs': 'photoUrls',
    'Icon URL': 'iconUrl',
    'Recent Reviews Count': 'recentReviewsCount',
    'Average Recent Rating': 'averageRecentRating',
    'Recent Reviews Text': 'recentReviewsText'
  }

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    const business: any = {}

    headers.forEach((header, index) => {
      const value = values[index] || ''
      const mappedField = columnMapping[header] || header.toLowerCase().replace(/\s+/g, '')

      // Convert specific fields to appropriate types
      if (mappedField === 'rating' || mappedField === 'latitude' || mappedField === 'longitude' || mappedField === 'averageRecentRating') {
        business[mappedField] = parseFloat(value) || 0
      } else if (mappedField === 'totalRatings' || mappedField === 'recentReviewsCount') {
        business[mappedField] = parseInt(value) || 0
      } else if (mappedField === 'types' && value) {
        business[mappedField] = value.split(',').map(t => t.trim())
      } else if (mappedField === 'weekdayText' && value) {
        business[mappedField] = value.split(';').map(h => h.trim())
      } else if (mappedField === 'photoUrls' && value) {
        business[mappedField] = value.split(',').map(u => u.trim()).filter(u => u)
      } else {
        business[mappedField] = value
      }
    })

    // Parse recent reviews text into review objects if available
    if (business.recentReviewsText) {
      business.reviews = parseRecentReviewsText(business.recentReviewsText)
    }

    businesses.push(business as ImportedBusiness)
  }

  return businesses
}

/**
 * Parse recent reviews text into review objects
 */
function parseRecentReviewsText(reviewsText: string): ImportedReview[] {
  if (!reviewsText) return []

  const reviews: ImportedReview[] = []
  const reviewBlocks = reviewsText.split('\n\n').filter(block => block.trim())

  for (const block of reviewBlocks) {
    const lines = block.trim().split('\n')
    if (lines.length === 0) continue

    const firstLine = lines[0]
    const ratingMatch = firstLine.match(/★+/)
    const authorMatch = firstLine.match(/★+ (.+?) \((.+?)\):/)

    if (ratingMatch && authorMatch) {
      const rating = ratingMatch[0].length
      const authorName = authorMatch[1]
      const timeDescription = authorMatch[2]
      const text = lines.slice(0).join('\n').replace(/★+ .+? \(.+?\): /, '')

      reviews.push({
        author_name: authorName,
        rating,
        text: text.trim(),
        time: Date.now() / 1000, // Approximate timestamp
        relative_time_description: timeDescription
      })
    }
  }

  return reviews.slice(0, 5) // Limit to 5 reviews
}
