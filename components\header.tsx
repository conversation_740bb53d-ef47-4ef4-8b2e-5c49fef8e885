"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { useUser } from "@/hooks/use-user"
import { Droplets, User, LogOut, MessageSquare } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export function Header() {
  const { user, profile, signOut } = useUser()

  return (
    <header className="border-b border-neutral-800 bg-neutral-900 sticky top-0 z-50">
      <div className="w-full px-6 py-4 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <div className="bg-blue-gradient p-2 rounded-xl glow-blue">
            <Droplets className="h-6 w-6 text-white" />
          </div>
          <span className="text-xl font-semibold text-white">PressureWash Pro</span>
        </Link>

        <nav className="hidden md:flex items-center space-x-6">
          <Link href="/search" className="text-neutral-400 hover:text-white transition-colors">
            Find Services
          </Link>
          <Link href="/how-it-works" className="text-neutral-400 hover:text-white transition-colors">
            How It Works
          </Link>
          <Link href="/for-businesses" className="text-neutral-400 hover:text-white transition-colors">
            For Businesses
          </Link>
        </nav>

        <div className="flex items-center space-x-4">
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="text-blue-400 hover:text-blue-300">
                  <User className="h-4 w-4 mr-2" />
                  {profile?.full_name || "Account"}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-neutral-900 border-neutral-800">
                <DropdownMenuItem asChild>
                  <Link href="/dashboard" className="text-neutral-300 hover:text-white">
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/messages" className="text-neutral-300 hover:text-white">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Messages
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={signOut} className="text-neutral-300 hover:text-white cursor-pointer">
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <>
              <Button variant="ghost" asChild className="text-blue-400 hover:text-blue-300">
                <Link href="/auth/login">Login</Link>
              </Button>
              <Button asChild className="bg-blue-gradient-hover">
                <Link href="/auth/signup">Sign Up</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  )
}
