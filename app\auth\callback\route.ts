import { createServerClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams, origin } = new URL(request.url)
    const code = searchParams.get('code')
    const next = searchParams.get('next') ?? '/dashboard'
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')
    
    // Handle auth errors
    if (error) {
      console.error('Auth callback error:', error, errorDescription)
      const errorUrl = new URL('/auth/login', origin)
      errorUrl.searchParams.set('error', errorDescription || error)
      return NextResponse.redirect(errorUrl)
    }
    
    if (!code) {
      console.error('No auth code provided in callback')
      const errorUrl = new URL('/auth/login', origin)
      errorUrl.searchParams.set('error', 'Authentication failed')
      return NextResponse.redirect(errorUrl)
    }
    
    const supabase = await createServerClient()
    
    if (!supabase) {
      console.error('Database connection not available')
      const errorUrl = new URL('/auth/login', origin)
      errorUrl.searchParams.set('error', 'Service temporarily unavailable')
      return NextResponse.redirect(errorUrl)
    }
    
    // Exchange the code for a session
    const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)
    
    if (exchangeError) {
      console.error('Code exchange error:', exchangeError)
      const errorUrl = new URL('/auth/login', origin)
      errorUrl.searchParams.set('error', 'Authentication failed')
      return NextResponse.redirect(errorUrl)
    }
    
    if (!data.user) {
      console.error('No user data after code exchange')
      const errorUrl = new URL('/auth/login', origin)
      errorUrl.searchParams.set('error', 'Authentication failed')
      return NextResponse.redirect(errorUrl)
    }
    
    // Check if this is a password reset flow
    const isPasswordReset = searchParams.get('type') === 'recovery'
    
    if (isPasswordReset) {
      // Redirect to password reset page
      return NextResponse.redirect(new URL('/auth/reset-password', origin))
    }
    
    // For email confirmation, check if user has a profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single()
    
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Profile fetch error:', profileError)
    }
    
    // If no profile exists, create one from user metadata
    if (!profile && data.user.user_metadata?.full_name) {
      try {
        await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            full_name: data.user.user_metadata.full_name,
            email: data.user.email,
          })
      } catch (insertError) {
        console.error('Profile creation error:', insertError)
        // Continue anyway - profile can be created later
      }
    }
    
    // Redirect to the intended destination
    return NextResponse.redirect(new URL(next, origin))
    
  } catch (error) {
    console.error('Auth callback error:', error)
    const errorUrl = new URL('/auth/login', request.url)
    errorUrl.searchParams.set('error', 'Authentication failed')
    return NextResponse.redirect(errorUrl)
  }
}