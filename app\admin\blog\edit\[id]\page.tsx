import { BlogEditor } from "@/components/admin/blog-editor"
import { AdminHeader } from "@/components/admin/admin-header"
import { AdminSidebar } from "@/components/admin/admin-sidebar"

interface EditBlogPostPageProps {
  params: Promise<{ id: string }>
}

export default async function EditBlogPostPage({ params }: EditBlogPostPageProps) {
  const { id } = await params
  return (
    <div className="min-h-screen bg-black">
      <AdminHeader />

      <div className="flex">
        <AdminSidebar activeTab="blog" />

        <main className="flex-1 p-6">
          <BlogEditor mode="edit" postId={id} />
        </main>
      </div>
    </div>
  )
}
