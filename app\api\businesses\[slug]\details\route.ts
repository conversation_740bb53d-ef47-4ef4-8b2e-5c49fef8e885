import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, getBusinessReviews } from '@/lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const { searchParams } = new URL(request.url)
    
    // Get pagination parameters for reviews
    const reviewLimit = searchParams.get('reviewLimit') ? parseInt(searchParams.get('reviewLimit')!) : 10
    const reviewOffset = searchParams.get('reviewOffset') ? parseInt(searchParams.get('reviewOffset')!) : 0
    
    // Get business details
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError) {
      console.error('Error fetching business details:', businessError)
      return NextResponse.json({ error: businessError.message }, { status: 500 })
    }
    
    if (!business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Get additional reviews with pagination
    const { reviews, error: reviewsError } = await getBusinessReviews(business.id, reviewLimit, reviewOffset)
    
    if (reviewsError) {
      console.error('Error fetching business reviews:', reviewsError)
      // Don't fail the entire request if reviews fail
    }
    
    // Combine business data with paginated reviews
    const businessWithDetails = {
      ...business,
      reviews: reviews || [],
      reviewsPagination: {
        limit: reviewLimit,
        offset: reviewOffset,
        hasMore: (reviews?.length || 0) === reviewLimit
      }
    }
    
    return NextResponse.json({ business: businessWithDetails })
  } catch (error) {
    console.error('Business details API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}