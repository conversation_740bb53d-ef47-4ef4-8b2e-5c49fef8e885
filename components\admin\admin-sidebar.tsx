"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { memo } from "react"
import { cn } from "@/lib/utils"
import { BarChart3, LayoutDashboard, Users, Building2, Star, Edit, Upload, MessageSquare } from "lucide-react"

export const AdminSidebar = memo(function AdminSidebar() {
  const pathname = usePathname()
  const navigationItems = [
    {
      name: "Dashboard",
      href: "/admin",
      icon: LayoutDashboard,
      key: "dashboard",
    },
    {
      name: "Users",
      href: "/admin/users",
      icon: Users,
      key: "users",
    },
    {
      name: "Businesses",
      href: "/admin/businesses",
      icon: Building2,
      key: "businesses",
    },
    {
      name: "Reviews",
      href: "/admin/reviews",
      icon: Star,
      key: "reviews",
    },
    {
      name: "Blog",
      href: "/admin/blog",
      icon: Edit,
      key: "blog",
    },
    {
      name: "Bulk Import",
      href: "/admin/bulk-import",
      icon: Upload,
      key: "bulk-import",
    },
    {
      name: "Messages",
      href: "/admin/messages",
      icon: MessageSquare,
      key: "messages",
    },
  ]

  return (
    <div className="w-64 bg-neutral-900 border-r border-neutral-800 min-h-screen admin-sidebar">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-white mb-6">Admin Panel</h2>
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.href

            return (
              <Link
                key={item.key}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium admin-nav-link",
                  isActive
                    ? "bg-blue-500/10 text-blue-400 border border-blue-500/20"
                    : "text-neutral-400 hover:bg-blue-500/5 hover:text-white"
                )}
              >
                <Icon className={cn(
                  "h-5 w-5 admin-icon",
                  isActive ? "text-blue-400" : "text-neutral-400"
                )} />
                <span>{item.name}</span>
              </Link>
            )
          })}
        </nav>
      </div>
    </div>
  )
})
