"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { SearchBar } from "@/components/search-bar"
import { BusinessCard } from "@/components/business-card"
import { MapView } from "@/components/map-view"
import { FilterPanel } from "@/components/filter-panel"
import { SortDropdown } from "@/components/sort-dropdown"
import { Button } from "@/components/ui/button"
import type { BusinessWithDetails } from "@/lib/types"
import { Loader2, ChevronLeft, ChevronRight } from "lucide-react"

export function SearchResults() {
  const searchParams = useSearchParams()
  const [businesses, setBusinesses] = useState<(BusinessWithDetails & { distance?: number })[]>([])
  const [loading, setLoading] = useState(true)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [filters, setFilters] = useState({
    services: [] as number[],
    rating: 0,
    premium: false,
  })
  const [sort, setSort] = useState({
    sortBy: 'rating' as 'distance' | 'rating' | 'reviews' | 'name',
    sortOrder: 'desc' as 'asc' | 'desc',
  })

  const limit = 20
  const offset = (currentPage - 1) * limit
  const totalPages = Math.ceil(total / limit)

  const query = searchParams.get("q") || ""
  const location = searchParams.get("location") || ""

  useEffect(() => {
    fetchBusinesses()
  }, [query, location, filters, currentPage, sort])

  const handleSortChange = (sortBy: 'distance' | 'rating' | 'reviews' | 'name', sortOrder: 'asc' | 'desc') => {
    setSort({ sortBy, sortOrder })
    setCurrentPage(1) // Reset to first page when sorting changes
  }

  const fetchBusinesses = async () => {
    setLoading(true)
    try {
      // Build search parameters
      const params = new URLSearchParams()
      if (query) params.set('query', query)
      if (location) params.set('location', location)
      if (filters.services.length > 0) {
        params.set('serviceIds', filters.services.join(','))
      }
      if (filters.rating > 0) {
        params.set('minRating', filters.rating.toString())
      }
      params.set('limit', limit.toString())
      params.set('offset', offset.toString())
      params.set('sortBy', sort.sortBy)
      params.set('sortOrder', sort.sortOrder)

      // Call the search API
      const response = await fetch(`/api/search?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Search failed')
      }

      const data = await response.json()
      setBusinesses(data.businesses || [])
      setTotal(data.total || 0)
    } catch (error) {
      console.error("Error fetching businesses:", error)
      setBusinesses([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Search Bar */}
      <div className="mb-6">
        <SearchBar initialQuery={query} initialLocation={location} />
      </div>

      {/* Results Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-white mb-2">
            {location ? `Pressure Washing Services in ${location}` : "Search Results"}
          </h1>
          <p className="text-neutral-400">
            {loading ? "Searching..." : `${total} businesses found`}
            {!loading && total > 0 && (
              <span className="ml-2 text-neutral-500">
                (Page {currentPage} of {totalPages})
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <SortDropdown currentSort={sort} onSortChange={handleSortChange} />
          <FilterPanel filters={filters} onFiltersChange={setFilters} />
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-[600px]">
        {/* Map View */}
        <div className="order-2 lg:order-1">
          <MapView businesses={businesses} />
        </div>

        {/* Business List */}
        <div className="order-1 lg:order-2">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
            </div>
          ) : businesses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-neutral-400 text-lg mb-4">No businesses found</p>
              <p className="text-neutral-500">Try adjusting your search criteria or location</p>
            </div>
          ) : (
            <div className="space-y-4">
              {businesses.map((business) => (
                <BusinessCard key={business.id} business={business} />
              ))}
            </div>
          )}

          {/* Pagination */}
          {!loading && totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + Math.max(1, currentPage - 2)
                  if (page > totalPages) return null

                  return (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className={page === currentPage
                        ? "bg-blue-600 text-white"
                        : "border-neutral-700 text-neutral-300 hover:bg-neutral-800"
                      }
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
